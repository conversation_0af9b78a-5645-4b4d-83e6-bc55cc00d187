import asyncio

# https://github.com/jlowin/fastmcp
from fastmcp import Client


async def main():
    async with Client("http://localhost:8000/mcp/") as client:
        tools = await client.list_tools()
        print(f"Available tools: {tools}")

        wiki_response = await client.call_tool("search_wikipedia_for_article", {"search_text": "Paris"})
        print(f"Result: {wiki_response}")


async def main_multiple_servers():
    # Standard MCP configuration with multiple servers
    config = {
        "mcpServers": {
            "wiki": {"url": "http://localhost:8000/mcp/"},
            "weather": {"url": "https://weather-api.example.com/mcp"},
            # "assistant": {"command": "python", "args": ["./assistant_server.py"]}
        }
    }

    # Create a client that connects to all servers
    client = Client(config)

    async with client:
        tools = await client.list_tools()
        print(f"Available tools: {tools}")

        # Access tools and resources with server prefixes
        wiki_response = await client.call_tool("wiki_test", {"string": "LeMoussel"})
        print(f"Result: {wiki_response}")

        # answer = await client.call_tool("assistant_answer_question", {"query": "What is MCP?"})


if __name__ == "__main__":
    asyncio.run(main())
